{"version": 3, "sources": ["../../.pnpm/three@0.179.1/node_modules/three/examples/jsm/geometries/TextGeometry.js"], "sourcesContent": ["import {\n\tExtrudeGeometry\n} from 'three';\n\n/**\n * A class for generating text as a single geometry. It is constructed by providing a string of text, and a set of\n * parameters consisting of a loaded font and extrude settings.\n *\n * See the {@link FontLoader} page for additional details.\n *\n * `TextGeometry` uses [typeface.json]{@link http://gero3.github.io/facetype.js/} generated fonts.\n * Some existing fonts can be found located in `/examples/fonts`.\n *\n * ```js\n * const loader = new FontLoader();\n * const font = await loader.loadAsync( 'fonts/helvetiker_regular.typeface.json' );\n * const geometry = new TextGeometry( 'Hello three.js!', {\n * \tfont: font,\n * \tsize: 80,\n * \tdepth: 5,\n * \tcurveSegments: 12\n * } );\n * ```\n *\n * @augments ExtrudeGeometry\n * @three_import import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';\n */\nclass TextGeometry extends ExtrudeGeometry {\n\n\t/**\n\t * Constructs a new text geometry.\n\t *\n\t * @param {string} text - The text that should be transformed into a geometry.\n\t * @param {TextGeometry~Options} [parameters] - The text settings.\n\t */\n\tconstructor( text, parameters = {} ) {\n\n\t\tconst font = parameters.font;\n\n\t\tif ( font === undefined ) {\n\n\t\t\tsuper(); // generate default extrude geometry\n\n\t\t} else {\n\n\t\t\tconst shapes = font.generateShapes( text, parameters.size );\n\n\t\t\t// defaults\n\n\t\t\tif ( parameters.depth === undefined ) parameters.depth = 50;\n\t\t\tif ( parameters.bevelThickness === undefined ) parameters.bevelThickness = 10;\n\t\t\tif ( parameters.bevelSize === undefined ) parameters.bevelSize = 8;\n\t\t\tif ( parameters.bevelEnabled === undefined ) parameters.bevelEnabled = false;\n\n\t\t\tsuper( shapes, parameters );\n\n\t\t}\n\n\t\tthis.type = 'TextGeometry';\n\n\t}\n\n}\n\n/**\n * Represents the `options` type of the geometry's constructor.\n *\n * @typedef {Object} TextGeometry~Options\n * @property {Font} [font] - The font.\n * @property {number} [size=100] - The text size.\n * @property {number} [depth=50] - Depth to extrude the shape.\n * @property {number} [curveSegments=12] - Number of points on the curves.\n * @property {number} [steps=1] - Number of points used for subdividing segments along the depth of the extruded spline.\n * @property {boolean} [bevelEnabled=false] - Whether to beveling to the shape or not.\n * @property {number} [bevelThickness=10] - How deep into the original shape the bevel goes.\n * @property {number} [bevelSize=8] - Distance from the shape outline that the bevel extends.\n * @property {number} [bevelOffset=0] - Distance from the shape outline that the bevel starts.\n * @property {number} [bevelSegments=3] - Number of bevel layers.\n * @property {?Curve} [extrudePath=null] - A 3D spline path along which the shape should be extruded. Bevels not supported for path extrusion.\n * @property {Object} [UVGenerator] - An object that provides UV generator functions for custom UV generation.\n **/\n\nexport { TextGeometry };\n"], "mappings": ";;;;;AA2BA,IAAM,eAAN,cAA2B,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1C,YAAa,MAAM,aAAa,CAAC,GAAI;AAEpC,UAAM,OAAO,WAAW;AAExB,QAAK,SAAS,QAAY;AAEzB,YAAM;AAAA,IAEP,OAAO;AAEN,YAAM,SAAS,KAAK,eAAgB,MAAM,WAAW,IAAK;AAI1D,UAAK,WAAW,UAAU,OAAY,YAAW,QAAQ;AACzD,UAAK,WAAW,mBAAmB,OAAY,YAAW,iBAAiB;AAC3E,UAAK,WAAW,cAAc,OAAY,YAAW,YAAY;AACjE,UAAK,WAAW,iBAAiB,OAAY,YAAW,eAAe;AAEvE,YAAO,QAAQ,UAAW;AAAA,IAE3B;AAEA,SAAK,OAAO;AAAA,EAEb;AAED;", "names": []}
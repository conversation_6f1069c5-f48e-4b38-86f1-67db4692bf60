{"version": 3, "sources": ["../../.pnpm/three@0.179.1/node_modules/three/examples/jsm/loaders/FontLoader.js"], "sourcesContent": ["import {\n\t<PERSON><PERSON><PERSON><PERSON>,\n\t<PERSON><PERSON>,\n\t<PERSON><PERSON><PERSON><PERSON><PERSON>\n} from 'three';\n\n/**\n * A loader for loading fonts.\n *\n * You can convert fonts online using [facetype.js]{@link https://gero3.github.io/facetype.js/}.\n *\n * ```js\n * const loader = new FontLoader();\n * const font = await loader.loadAsync( 'fonts/helvetiker_regular.typeface.json' );\n * ```\n *\n * @augments Loader\n * @three_import import { FontLoader } from 'three/addons/loaders/FontLoader.js';\n */\nclass FontLoader extends Loader {\n\n\t/**\n\t * Constructs a new font loader.\n\t *\n\t * @param {LoadingManager} [manager] - The loading manager.\n\t */\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t}\n\n\t/**\n\t * Starts loading from the given URL and passes the loaded font\n\t * to the `onLoad()` callback.\n\t *\n\t * @param {string} url - The path/URL of the file to be loaded. This can also be a data URI.\n\t * @param {function(Font)} onLoad - Executed when the loading process has been finished.\n\t * @param {onProgressCallback} onProgress - Executed while the loading is in progress.\n\t * @param {onErrorCallback} onError - Executed when errors occur.\n\t */\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tconst loader = new FileLoader( this.manager );\n\t\tloader.setPath( this.path );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\t\tloader.load( url, function ( text ) {\n\n\t\t\tconst font = scope.parse( JSON.parse( text ) );\n\n\t\t\tif ( onLoad ) onLoad( font );\n\n\t\t}, onProgress, onError );\n\n\t}\n\n\t/**\n\t * Parses the given font data and returns the resulting font.\n\t *\n\t * @param {Object} json - The raw font data as a JSON object.\n\t * @return {Font} The font.\n\t */\n\tparse( json ) {\n\n\t\treturn new Font( json );\n\n\t}\n\n}\n\n/**\n * Class representing a font.\n */\nclass Font {\n\n\t/**\n\t * Constructs a new font.\n\t *\n\t * @param {Object} data - The font data as JSON.\n\t */\n\tconstructor( data ) {\n\n\t\t/**\n\t\t * This flag can be used for type testing.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @readonly\n\t\t * @default true\n\t\t */\n\t\tthis.isFont = true;\n\n\t\tthis.type = 'Font';\n\n\t\t/**\n\t\t * The font data as JSON.\n\t\t *\n\t\t * @type {Object}\n\t\t */\n\t\tthis.data = data;\n\n\t}\n\n\t/**\n\t * Generates geometry shapes from the given text and size. The result of this method\n\t * should be used with {@link ShapeGeometry} to generate the actual geometry data.\n\t *\n\t * @param {string} text - The text.\n\t * @param {number} [size=100] - The text size.\n\t * @return {Array<Shape>} An array of shapes representing the text.\n\t */\n\tgenerateShapes( text, size = 100 ) {\n\n\t\tconst shapes = [];\n\t\tconst paths = createPaths( text, size, this.data );\n\n\t\tfor ( let p = 0, pl = paths.length; p < pl; p ++ ) {\n\n\t\t\tshapes.push( ...paths[ p ].toShapes() );\n\n\t\t}\n\n\t\treturn shapes;\n\n\t}\n\n}\n\nfunction createPaths( text, size, data ) {\n\n\tconst chars = Array.from( text );\n\tconst scale = size / data.resolution;\n\tconst line_height = ( data.boundingBox.yMax - data.boundingBox.yMin + data.underlineThickness ) * scale;\n\n\tconst paths = [];\n\n\tlet offsetX = 0, offsetY = 0;\n\n\tfor ( let i = 0; i < chars.length; i ++ ) {\n\n\t\tconst char = chars[ i ];\n\n\t\tif ( char === '\\n' ) {\n\n\t\t\toffsetX = 0;\n\t\t\toffsetY -= line_height;\n\n\t\t} else {\n\n\t\t\tconst ret = createPath( char, scale, offsetX, offsetY, data );\n\t\t\toffsetX += ret.offsetX;\n\t\t\tpaths.push( ret.path );\n\n\t\t}\n\n\t}\n\n\treturn paths;\n\n}\n\nfunction createPath( char, scale, offsetX, offsetY, data ) {\n\n\tconst glyph = data.glyphs[ char ] || data.glyphs[ '?' ];\n\n\tif ( ! glyph ) {\n\n\t\tconsole.error( 'THREE.Font: character \"' + char + '\" does not exists in font family ' + data.familyName + '.' );\n\n\t\treturn;\n\n\t}\n\n\tconst path = new ShapePath();\n\n\tlet x, y, cpx, cpy, cpx1, cpy1, cpx2, cpy2;\n\n\tif ( glyph.o ) {\n\n\t\tconst outline = glyph._cachedOutline || ( glyph._cachedOutline = glyph.o.split( ' ' ) );\n\n\t\tfor ( let i = 0, l = outline.length; i < l; ) {\n\n\t\t\tconst action = outline[ i ++ ];\n\n\t\t\tswitch ( action ) {\n\n\t\t\t\tcase 'm': // moveTo\n\n\t\t\t\t\tx = outline[ i ++ ] * scale + offsetX;\n\t\t\t\t\ty = outline[ i ++ ] * scale + offsetY;\n\n\t\t\t\t\tpath.moveTo( x, y );\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'l': // lineTo\n\n\t\t\t\t\tx = outline[ i ++ ] * scale + offsetX;\n\t\t\t\t\ty = outline[ i ++ ] * scale + offsetY;\n\n\t\t\t\t\tpath.lineTo( x, y );\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'q': // quadraticCurveTo\n\n\t\t\t\t\tcpx = outline[ i ++ ] * scale + offsetX;\n\t\t\t\t\tcpy = outline[ i ++ ] * scale + offsetY;\n\t\t\t\t\tcpx1 = outline[ i ++ ] * scale + offsetX;\n\t\t\t\t\tcpy1 = outline[ i ++ ] * scale + offsetY;\n\n\t\t\t\t\tpath.quadraticCurveTo( cpx1, cpy1, cpx, cpy );\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'b': // bezierCurveTo\n\n\t\t\t\t\tcpx = outline[ i ++ ] * scale + offsetX;\n\t\t\t\t\tcpy = outline[ i ++ ] * scale + offsetY;\n\t\t\t\t\tcpx1 = outline[ i ++ ] * scale + offsetX;\n\t\t\t\t\tcpy1 = outline[ i ++ ] * scale + offsetY;\n\t\t\t\t\tcpx2 = outline[ i ++ ] * scale + offsetX;\n\t\t\t\t\tcpy2 = outline[ i ++ ] * scale + offsetY;\n\n\t\t\t\t\tpath.bezierCurveTo( cpx1, cpy1, cpx2, cpy2, cpx, cpy );\n\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn { offsetX: glyph.ha * scale, path: path };\n\n}\n\nexport { FontLoader, Font };\n"], "mappings": ";;;;;;;AAmBA,IAAM,aAAN,cAAyB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO/B,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAAA,EAEhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAC5C,WAAO,QAAS,KAAK,IAAK;AAC1B,WAAO,iBAAkB,KAAK,aAAc;AAC5C,WAAO,mBAAoB,KAAK,eAAgB;AAChD,WAAO,KAAM,KAAK,SAAW,MAAO;AAEnC,YAAM,OAAO,MAAM,MAAO,KAAK,MAAO,IAAK,CAAE;AAE7C,UAAK,OAAS,QAAQ,IAAK;AAAA,IAE5B,GAAG,YAAY,OAAQ;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAO,MAAO;AAEb,WAAO,IAAI,KAAM,IAAK;AAAA,EAEvB;AAED;AAKA,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOV,YAAa,MAAO;AASnB,SAAK,SAAS;AAEd,SAAK,OAAO;AAOZ,SAAK,OAAO;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAgB,MAAM,OAAO,KAAM;AAElC,UAAM,SAAS,CAAC;AAChB,UAAM,QAAQ,YAAa,MAAM,MAAM,KAAK,IAAK;AAEjD,aAAU,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAO;AAElD,aAAO,KAAM,GAAG,MAAO,CAAE,EAAE,SAAS,CAAE;AAAA,IAEvC;AAEA,WAAO;AAAA,EAER;AAED;AAEA,SAAS,YAAa,MAAM,MAAM,MAAO;AAExC,QAAM,QAAQ,MAAM,KAAM,IAAK;AAC/B,QAAM,QAAQ,OAAO,KAAK;AAC1B,QAAM,eAAgB,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,sBAAuB;AAElG,QAAM,QAAQ,CAAC;AAEf,MAAI,UAAU,GAAG,UAAU;AAE3B,WAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAO;AAEzC,UAAM,OAAO,MAAO,CAAE;AAEtB,QAAK,SAAS,MAAO;AAEpB,gBAAU;AACV,iBAAW;AAAA,IAEZ,OAAO;AAEN,YAAM,MAAM,WAAY,MAAM,OAAO,SAAS,SAAS,IAAK;AAC5D,iBAAW,IAAI;AACf,YAAM,KAAM,IAAI,IAAK;AAAA,IAEtB;AAAA,EAED;AAEA,SAAO;AAER;AAEA,SAAS,WAAY,MAAM,OAAO,SAAS,SAAS,MAAO;AAE1D,QAAM,QAAQ,KAAK,OAAQ,IAAK,KAAK,KAAK,OAAQ,GAAI;AAEtD,MAAK,CAAE,OAAQ;AAEd,YAAQ,MAAO,4BAA4B,OAAO,sCAAsC,KAAK,aAAa,GAAI;AAE9G;AAAA,EAED;AAEA,QAAM,OAAO,IAAI,UAAU;AAE3B,MAAI,GAAG,GAAG,KAAK,KAAK,MAAM,MAAM,MAAM;AAEtC,MAAK,MAAM,GAAI;AAEd,UAAM,UAAU,MAAM,mBAAoB,MAAM,iBAAiB,MAAM,EAAE,MAAO,GAAI;AAEpF,aAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,KAAK;AAE7C,YAAM,SAAS,QAAS,GAAK;AAE7B,cAAS,QAAS;AAAA,QAEjB,KAAK;AAEJ,cAAI,QAAS,GAAK,IAAI,QAAQ;AAC9B,cAAI,QAAS,GAAK,IAAI,QAAQ;AAE9B,eAAK,OAAQ,GAAG,CAAE;AAElB;AAAA,QAED,KAAK;AAEJ,cAAI,QAAS,GAAK,IAAI,QAAQ;AAC9B,cAAI,QAAS,GAAK,IAAI,QAAQ;AAE9B,eAAK,OAAQ,GAAG,CAAE;AAElB;AAAA,QAED,KAAK;AAEJ,gBAAM,QAAS,GAAK,IAAI,QAAQ;AAChC,gBAAM,QAAS,GAAK,IAAI,QAAQ;AAChC,iBAAO,QAAS,GAAK,IAAI,QAAQ;AACjC,iBAAO,QAAS,GAAK,IAAI,QAAQ;AAEjC,eAAK,iBAAkB,MAAM,MAAM,KAAK,GAAI;AAE5C;AAAA,QAED,KAAK;AAEJ,gBAAM,QAAS,GAAK,IAAI,QAAQ;AAChC,gBAAM,QAAS,GAAK,IAAI,QAAQ;AAChC,iBAAO,QAAS,GAAK,IAAI,QAAQ;AACjC,iBAAO,QAAS,GAAK,IAAI,QAAQ;AACjC,iBAAO,QAAS,GAAK,IAAI,QAAQ;AACjC,iBAAO,QAAS,GAAK,IAAI,QAAQ;AAEjC,eAAK,cAAe,MAAM,MAAM,MAAM,MAAM,KAAK,GAAI;AAErD;AAAA,MAEF;AAAA,IAED;AAAA,EAED;AAEA,SAAO,EAAE,SAAS,MAAM,KAAK,OAAO,KAAW;AAEhD;", "names": []}
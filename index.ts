import * as THREE from "three";
import { TextGeometry } from "three/examples/jsm/geometries/TextGeometry.js";
import { FontLoader } from "three/examples/jsm/loaders/FontLoader.js";
import { DragControls } from "three/examples/jsm/controls/DragControls.js";

const scene = new THREE.Scene();
// 添加光源, 默认朝向 (0,0,0)
{
  const light = new THREE.DirectionalLight(0xffffff, 1);
  light.position.set(1, 1, 1);
  scene.add(light);
}
// 添加相机, 相机朝向负Z方向, 也就是朝向你
const camera = new THREE.PerspectiveCamera(
  75,
  window.innerWidth / window.innerHeight,
  0.1,
  1000
);
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

const geometry = new THREE.BoxGeometry();
const material = new THREE.MeshPhongMaterial({
  color: 0x00ffff,
});
const cube = new THREE.Mesh(geometry, material);
cube.position.z = 1;
scene.add(cube);

camera.position.z = 4;

function animate() {
  requestAnimationFrame(animate);
  cube.rotation.x += 0.01;
  cube.rotation.y += 0.01;
  renderer.render(scene, camera);
}
animate();

// 加载字体
const loader = new FontLoader();
loader.load(
  "https://threejs.org/examples/fonts/helvetiker_regular.typeface.json",
  function (font) {
    const textGeo = new TextGeometry("Hello", {
      font: font,
      size: 0.6,
      depth: 0.2,
    });

    const textMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
    const textMesh = new THREE.Mesh(textGeo, textMaterial);
    textMesh.position.set(-4, 0, 0);
    scene.add(textMesh);

    // 添加拖拽控制
    const dragControls = new DragControls([textMesh], camera, renderer.domElement);
    
    // 拖拽开始时高亮
    dragControls.addEventListener('dragstart', function (event) {
      event.object.material.emissive.set(0x444444);
    });
    
    // 拖拽结束时取消高亮
    dragControls.addEventListener('dragend', function (event) {
      event.object.material.emissive.set(0x000000);
    });
  }
);

import { createRequire as ___createRequire } from 'module'; const require = ___createRequire(import.meta.url);
import { addManuallyIncludedOptimizeDeps, addOptimizedDepInfo, cleanupDepsCacheStaleDirs, createIsOptimizedDepFile, createIsOptimizedDepUrl, depsFromOptimizedDepInfo, depsLogString, discoverProjectDependencies, extractExportsData, getDepsCacheDir, getOptimizedDepPath, initDepsOptimizerMetadata, isDepOptimizationDisabled, loadCachedDepOptimizationMetadata, optimizeDeps, optimizeExplicitEnvironmentDeps, optimizedDepInfoFromFile, optimizedDepInfoFromId, optimizedDepNeedsInterop, runOptimizeDeps, toDiscoveredDependencies } from "./dep-BHkUv4Z8.js";
import "./dep-Ctugieod.js";

export { optimizeDeps };
{"version": 3, "sources": ["../../.pnpm/three@0.179.1/node_modules/three/examples/jsm/controls/DragControls.js"], "sourcesContent": ["import {\n\t<PERSON>s,\n\tMatrix4,\n\tPlane,\n\tRaycaster,\n\tVector2,\n\tVector3,\n\t<PERSON><PERSON><PERSON><PERSON>,\n\tTOUCH\n} from 'three';\n\nconst _plane = new Plane();\n\nconst _pointer = new Vector2();\nconst _offset = new Vector3();\nconst _diff = new Vector2();\nconst _previousPointer = new Vector2();\nconst _intersection = new Vector3();\nconst _worldPosition = new Vector3();\nconst _inverseMatrix = new Matrix4();\n\nconst _up = new Vector3();\nconst _right = new Vector3();\n\nlet _selected = null, _hovered = null;\nconst _intersections = [];\n\nconst STATE = {\n\tNONE: - 1,\n\tPAN: 0,\n\tROTATE: 1\n};\n\n/**\n * This class can be used to provide a drag'n'drop interaction.\n *\n * ```js\n * const controls = new DragControls( objects, camera, renderer.domElement );\n *\n * // add event listener to highlight dragged objects\n * controls.addEventListener( 'dragstart', function ( event ) {\n *\n * \tevent.object.material.emissive.set( 0xaaaaaa );\n *\n * } );\n *\n * controls.addEventListener( 'dragend', function ( event ) {\n *\n * \tevent.object.material.emissive.set( 0x000000 );\n *\n * } );\n * ```\n *\n * @augments Controls\n * @three_import import { DragControls } from 'three/addons/controls/DragControls.js';\n */\nclass DragControls extends Controls {\n\n\t/**\n\t * Constructs a new controls instance.\n\t *\n\t * @param {Array<Object3D>} objects - An array of draggable 3D objects.\n\t * @param {Camera} camera - The camera of the rendered scene.\n\t * @param {?HTMLDOMElement} [domElement=null] - The HTML DOM element used for event listeners.\n\t */\n\tconstructor( objects, camera, domElement = null ) {\n\n\t\tsuper( camera, domElement );\n\n\t\t/**\n\t\t * An array of draggable 3D objects.\n\t\t *\n\t\t * @type {Array<Object3D>}\n\t\t */\n\t\tthis.objects = objects;\n\n\t\t/**\n\t\t * Whether children of draggable objects can be dragged independently from their parent.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.recursive = true;\n\n\t\t/**\n\t\t * This option only works if the `objects` array contains a single draggable  group object.\n\t\t * If set to `true`, the controls does not transform individual objects but the entire group.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.transformGroup = false;\n\n\t\t/**\n\t\t * The speed at which the object will rotate when dragged in `rotate` mode.\n\t\t * The higher the number the faster the rotation.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 1\n\t\t */\n\t\tthis.rotateSpeed = 1;\n\n\t\t/**\n\t\t * The raycaster used for detecting 3D objects.\n\t\t *\n\t\t * @type {Raycaster}\n\t\t */\n\t\tthis.raycaster = new Raycaster();\n\n\t\t// interaction\n\n\t\tthis.mouseButtons = { LEFT: MOUSE.PAN, MIDDLE: MOUSE.PAN, RIGHT: MOUSE.ROTATE };\n\t\tthis.touches = { ONE: TOUCH.PAN };\n\n\t\t// event listeners\n\n\t\tthis._onPointerMove = onPointerMove.bind( this );\n\t\tthis._onPointerDown = onPointerDown.bind( this );\n\t\tthis._onPointerCancel = onPointerCancel.bind( this );\n\t\tthis._onContextMenu = onContextMenu.bind( this );\n\n\t\t//\n\n\t\tif ( domElement !== null ) {\n\n\t\t\tthis.connect( domElement );\n\n\t\t}\n\n\t}\n\n\tconnect( element ) {\n\n\t\tsuper.connect( element );\n\n\t\tthis.domElement.addEventListener( 'pointermove', this._onPointerMove );\n\t\tthis.domElement.addEventListener( 'pointerdown', this._onPointerDown );\n\t\tthis.domElement.addEventListener( 'pointerup', this._onPointerCancel );\n\t\tthis.domElement.addEventListener( 'pointerleave', this._onPointerCancel );\n\t\tthis.domElement.addEventListener( 'contextmenu', this._onContextMenu );\n\n\t\tthis.domElement.style.touchAction = 'none'; // disable touch scroll\n\n\t}\n\n\tdisconnect() {\n\n\t\tthis.domElement.removeEventListener( 'pointermove', this._onPointerMove );\n\t\tthis.domElement.removeEventListener( 'pointerdown', this._onPointerDown );\n\t\tthis.domElement.removeEventListener( 'pointerup', this._onPointerCancel );\n\t\tthis.domElement.removeEventListener( 'pointerleave', this._onPointerCancel );\n\t\tthis.domElement.removeEventListener( 'contextmenu', this._onContextMenu );\n\n\t\tthis.domElement.style.touchAction = 'auto';\n\t\tthis.domElement.style.cursor = '';\n\n\t}\n\n\tdispose() {\n\n\t\tthis.disconnect();\n\n\t}\n\n\t_updatePointer( event ) {\n\n\t\tconst rect = this.domElement.getBoundingClientRect();\n\n\t\t_pointer.x = ( event.clientX - rect.left ) / rect.width * 2 - 1;\n\t\t_pointer.y = - ( event.clientY - rect.top ) / rect.height * 2 + 1;\n\n\t}\n\n\t_updateState( event ) {\n\n\t\t// determine action\n\n\t\tlet action;\n\n\t\tif ( event.pointerType === 'touch' ) {\n\n\t\t\taction = this.touches.ONE;\n\n\t\t} else {\n\n\t\t\tswitch ( event.button ) {\n\n\t\t\t\tcase 0:\n\n\t\t\t\t\taction = this.mouseButtons.LEFT;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 1:\n\n\t\t\t\t\taction = this.mouseButtons.MIDDLE;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 2:\n\n\t\t\t\t\taction = this.mouseButtons.RIGHT;\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\n\t\t\t\t\taction = null;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// determine state\n\n\t\tswitch ( action ) {\n\n\t\t\tcase MOUSE.PAN:\n\t\t\tcase TOUCH.PAN:\n\n\t\t\t\tthis.state = STATE.PAN;\n\n\t\t\t\tbreak;\n\n\t\t\tcase MOUSE.ROTATE:\n\t\t\tcase TOUCH.ROTATE:\n\n\t\t\t\tthis.state = STATE.ROTATE;\n\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\n\t\t\t\tthis.state = STATE.NONE;\n\n\t\t}\n\n\t}\n\n\tgetRaycaster() {\n\n\t\tconsole.warn( 'THREE.DragControls: getRaycaster() has been deprecated. Use controls.raycaster instead.' ); // @deprecated r169\n\n\t\treturn this.raycaster;\n\n\t}\n\n\tsetObjects( objects ) {\n\n\t\tconsole.warn( 'THREE.DragControls: setObjects() has been deprecated. Use controls.objects instead.' ); // @deprecated r169\n\n\t\tthis.objects = objects;\n\n\t}\n\n\tgetObjects() {\n\n\t\tconsole.warn( 'THREE.DragControls: getObjects() has been deprecated. Use controls.objects instead.' ); // @deprecated r169\n\n\t\treturn this.objects;\n\n\t}\n\n\tactivate() {\n\n\t\tconsole.warn( 'THREE.DragControls: activate() has been renamed to connect().' ); // @deprecated r169\n\t\tthis.connect();\n\n\t}\n\n\tdeactivate() {\n\n\t\tconsole.warn( 'THREE.DragControls: deactivate() has been renamed to disconnect().' ); // @deprecated r169\n\t\tthis.disconnect();\n\n\t}\n\n\tset mode( value ) {\n\n\t\tconsole.warn( 'THREE.DragControls: The .mode property has been removed. Define the type of transformation via the .mouseButtons or .touches properties.' ); // @deprecated r169\n\n\t}\n\n\tget mode() {\n\n\t\tconsole.warn( 'THREE.DragControls: The .mode property has been removed. Define the type of transformation via the .mouseButtons or .touches properties.' ); // @deprecated r169\n\n\t}\n\n}\n\nfunction onPointerMove( event ) {\n\n\tconst camera = this.object;\n\tconst domElement = this.domElement;\n\tconst raycaster = this.raycaster;\n\n\tif ( this.enabled === false ) return;\n\n\tthis._updatePointer( event );\n\n\traycaster.setFromCamera( _pointer, camera );\n\n\tif ( _selected ) {\n\n\t\tif ( this.state === STATE.PAN ) {\n\n\t\t\tif ( raycaster.ray.intersectPlane( _plane, _intersection ) ) {\n\n\t\t\t\t_selected.position.copy( _intersection.sub( _offset ).applyMatrix4( _inverseMatrix ) );\n\n\t\t\t}\n\n\t\t} else if ( this.state === STATE.ROTATE ) {\n\n\t\t\t_diff.subVectors( _pointer, _previousPointer ).multiplyScalar( this.rotateSpeed );\n\t\t\t_selected.rotateOnWorldAxis( _up, _diff.x );\n\t\t\t_selected.rotateOnWorldAxis( _right.normalize(), - _diff.y );\n\n\t\t}\n\n\t\tthis.dispatchEvent( { type: 'drag', object: _selected } );\n\n\t\t_previousPointer.copy( _pointer );\n\n\t} else {\n\n\t\t// hover support\n\n\t\tif ( event.pointerType === 'mouse' || event.pointerType === 'pen' ) {\n\n\t\t\t_intersections.length = 0;\n\n\t\t\traycaster.setFromCamera( _pointer, camera );\n\t\t\traycaster.intersectObjects( this.objects, this.recursive, _intersections );\n\n\t\t\tif ( _intersections.length > 0 ) {\n\n\t\t\t\tconst object = _intersections[ 0 ].object;\n\n\t\t\t\t_plane.setFromNormalAndCoplanarPoint( camera.getWorldDirection( _plane.normal ), _worldPosition.setFromMatrixPosition( object.matrixWorld ) );\n\n\t\t\t\tif ( _hovered !== object && _hovered !== null ) {\n\n\t\t\t\t\tthis.dispatchEvent( { type: 'hoveroff', object: _hovered } );\n\n\t\t\t\t\tdomElement.style.cursor = 'auto';\n\t\t\t\t\t_hovered = null;\n\n\t\t\t\t}\n\n\t\t\t\tif ( _hovered !== object ) {\n\n\t\t\t\t\tthis.dispatchEvent( { type: 'hoveron', object: object } );\n\n\t\t\t\t\tdomElement.style.cursor = 'pointer';\n\t\t\t\t\t_hovered = object;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tif ( _hovered !== null ) {\n\n\t\t\t\t\tthis.dispatchEvent( { type: 'hoveroff', object: _hovered } );\n\n\t\t\t\t\tdomElement.style.cursor = 'auto';\n\t\t\t\t\t_hovered = null;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t_previousPointer.copy( _pointer );\n\n}\n\nfunction onPointerDown( event ) {\n\n\tconst camera = this.object;\n\tconst domElement = this.domElement;\n\tconst raycaster = this.raycaster;\n\n\tif ( this.enabled === false ) return;\n\n\tthis._updatePointer( event );\n\tthis._updateState( event );\n\n\t_intersections.length = 0;\n\n\traycaster.setFromCamera( _pointer, camera );\n\traycaster.intersectObjects( this.objects, this.recursive, _intersections );\n\n\tif ( _intersections.length > 0 ) {\n\n\t\tif ( this.transformGroup === true ) {\n\n\t\t\t// look for the outermost group in the object's upper hierarchy\n\n\t\t\t_selected = findGroup( _intersections[ 0 ].object );\n\n\t\t} else {\n\n\t\t\t_selected = _intersections[ 0 ].object;\n\n\t\t}\n\n\t\t_plane.setFromNormalAndCoplanarPoint( camera.getWorldDirection( _plane.normal ), _worldPosition.setFromMatrixPosition( _selected.matrixWorld ) );\n\n\t\tif ( raycaster.ray.intersectPlane( _plane, _intersection ) ) {\n\n\t\t\tif ( this.state === STATE.PAN ) {\n\n\t\t\t\t_inverseMatrix.copy( _selected.parent.matrixWorld ).invert();\n\t\t\t\t_offset.copy( _intersection ).sub( _worldPosition.setFromMatrixPosition( _selected.matrixWorld ) );\n\n\t\t\t} else if ( this.state === STATE.ROTATE ) {\n\n\t\t\t\t// the controls only support Y+ up\n\t\t\t\t_up.set( 0, 1, 0 ).applyQuaternion( camera.quaternion ).normalize();\n\t\t\t\t_right.set( 1, 0, 0 ).applyQuaternion( camera.quaternion ).normalize();\n\n\t\t\t}\n\n\t\t}\n\n\t\tdomElement.style.cursor = 'move';\n\n\t\tthis.dispatchEvent( { type: 'dragstart', object: _selected } );\n\n\t}\n\n\t_previousPointer.copy( _pointer );\n\n}\n\nfunction onPointerCancel() {\n\n\tif ( this.enabled === false ) return;\n\n\tif ( _selected ) {\n\n\t\tthis.dispatchEvent( { type: 'dragend', object: _selected } );\n\n\t\t_selected = null;\n\n\t}\n\n\tthis.domElement.style.cursor = _hovered ? 'pointer' : 'auto';\n\n\tthis.state = STATE.NONE;\n\n}\n\nfunction onContextMenu( event ) {\n\n\tif ( this.enabled === false ) return;\n\n\tevent.preventDefault();\n\n}\n\nfunction findGroup( obj, group = null ) {\n\n\tif ( obj.isGroup ) group = obj;\n\n\tif ( obj.parent === null ) return group;\n\n\treturn findGroup( obj.parent, group );\n\n}\n\n/**\n * Fires when the user drags a 3D object.\n *\n * @event DragControls#drag\n * @type {Object}\n */\n\n/**\n * Fires when the user has finished dragging a 3D object.\n *\n * @event DragControls#dragend\n * @type {Object}\n */\n\n/**\n * Fires when the pointer is moved onto a 3D object, or onto one of its children.\n *\n * @event DragControls#hoveron\n * @type {Object}\n */\n\n/**\n * Fires when the pointer is moved out of a 3D object.\n *\n * @event DragControls#hoveroff\n * @type {Object}\n */\n\nexport { DragControls };\n"], "mappings": ";;;;;;;;;;;;AAWA,IAAM,SAAS,IAAI,MAAM;AAEzB,IAAM,WAAW,IAAI,QAAQ;AAC7B,IAAM,UAAU,IAAI,QAAQ;AAC5B,IAAM,QAAQ,IAAI,QAAQ;AAC1B,IAAM,mBAAmB,IAAI,QAAQ;AACrC,IAAM,gBAAgB,IAAI,QAAQ;AAClC,IAAM,iBAAiB,IAAI,QAAQ;AACnC,IAAM,iBAAiB,IAAI,QAAQ;AAEnC,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,SAAS,IAAI,QAAQ;AAE3B,IAAI,YAAY;AAAhB,IAAsB,WAAW;AACjC,IAAM,iBAAiB,CAAC;AAExB,IAAM,QAAQ;AAAA,EACb,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AACT;AAyBA,IAAM,eAAN,cAA2B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnC,YAAa,SAAS,QAAQ,aAAa,MAAO;AAEjD,UAAO,QAAQ,UAAW;AAO1B,SAAK,UAAU;AAQf,SAAK,YAAY;AASjB,SAAK,iBAAiB;AAStB,SAAK,cAAc;AAOnB,SAAK,YAAY,IAAI,UAAU;AAI/B,SAAK,eAAe,EAAE,MAAM,MAAM,KAAK,QAAQ,MAAM,KAAK,OAAO,MAAM,OAAO;AAC9E,SAAK,UAAU,EAAE,KAAK,MAAM,IAAI;AAIhC,SAAK,iBAAiB,cAAc,KAAM,IAAK;AAC/C,SAAK,iBAAiB,cAAc,KAAM,IAAK;AAC/C,SAAK,mBAAmB,gBAAgB,KAAM,IAAK;AACnD,SAAK,iBAAiB,cAAc,KAAM,IAAK;AAI/C,QAAK,eAAe,MAAO;AAE1B,WAAK,QAAS,UAAW;AAAA,IAE1B;AAAA,EAED;AAAA,EAEA,QAAS,SAAU;AAElB,UAAM,QAAS,OAAQ;AAEvB,SAAK,WAAW,iBAAkB,eAAe,KAAK,cAAe;AACrE,SAAK,WAAW,iBAAkB,eAAe,KAAK,cAAe;AACrE,SAAK,WAAW,iBAAkB,aAAa,KAAK,gBAAiB;AACrE,SAAK,WAAW,iBAAkB,gBAAgB,KAAK,gBAAiB;AACxE,SAAK,WAAW,iBAAkB,eAAe,KAAK,cAAe;AAErE,SAAK,WAAW,MAAM,cAAc;AAAA,EAErC;AAAA,EAEA,aAAa;AAEZ,SAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AACxE,SAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AACxE,SAAK,WAAW,oBAAqB,aAAa,KAAK,gBAAiB;AACxE,SAAK,WAAW,oBAAqB,gBAAgB,KAAK,gBAAiB;AAC3E,SAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AAExE,SAAK,WAAW,MAAM,cAAc;AACpC,SAAK,WAAW,MAAM,SAAS;AAAA,EAEhC;AAAA,EAEA,UAAU;AAET,SAAK,WAAW;AAAA,EAEjB;AAAA,EAEA,eAAgB,OAAQ;AAEvB,UAAM,OAAO,KAAK,WAAW,sBAAsB;AAEnD,aAAS,KAAM,MAAM,UAAU,KAAK,QAAS,KAAK,QAAQ,IAAI;AAC9D,aAAS,IAAI,EAAI,MAAM,UAAU,KAAK,OAAQ,KAAK,SAAS,IAAI;AAAA,EAEjE;AAAA,EAEA,aAAc,OAAQ;AAIrB,QAAI;AAEJ,QAAK,MAAM,gBAAgB,SAAU;AAEpC,eAAS,KAAK,QAAQ;AAAA,IAEvB,OAAO;AAEN,cAAS,MAAM,QAAS;AAAA,QAEvB,KAAK;AAEJ,mBAAS,KAAK,aAAa;AAC3B;AAAA,QAED,KAAK;AAEJ,mBAAS,KAAK,aAAa;AAC3B;AAAA,QAED,KAAK;AAEJ,mBAAS,KAAK,aAAa;AAC3B;AAAA,QAED;AAEC,mBAAS;AAAA,MAEX;AAAA,IAED;AAIA,YAAS,QAAS;AAAA,MAEjB,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAEV,aAAK,QAAQ,MAAM;AAEnB;AAAA,MAED,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAEV,aAAK,QAAQ,MAAM;AAEnB;AAAA,MAED;AAEC,aAAK,QAAQ,MAAM;AAAA,IAErB;AAAA,EAED;AAAA,EAEA,eAAe;AAEd,YAAQ,KAAM,yFAA0F;AAExG,WAAO,KAAK;AAAA,EAEb;AAAA,EAEA,WAAY,SAAU;AAErB,YAAQ,KAAM,qFAAsF;AAEpG,SAAK,UAAU;AAAA,EAEhB;AAAA,EAEA,aAAa;AAEZ,YAAQ,KAAM,qFAAsF;AAEpG,WAAO,KAAK;AAAA,EAEb;AAAA,EAEA,WAAW;AAEV,YAAQ,KAAM,+DAAgE;AAC9E,SAAK,QAAQ;AAAA,EAEd;AAAA,EAEA,aAAa;AAEZ,YAAQ,KAAM,oEAAqE;AACnF,SAAK,WAAW;AAAA,EAEjB;AAAA,EAEA,IAAI,KAAM,OAAQ;AAEjB,YAAQ,KAAM,0IAA2I;AAAA,EAE1J;AAAA,EAEA,IAAI,OAAO;AAEV,YAAQ,KAAM,0IAA2I;AAAA,EAE1J;AAED;AAEA,SAAS,cAAe,OAAQ;AAE/B,QAAM,SAAS,KAAK;AACpB,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AAEvB,MAAK,KAAK,YAAY,MAAQ;AAE9B,OAAK,eAAgB,KAAM;AAE3B,YAAU,cAAe,UAAU,MAAO;AAE1C,MAAK,WAAY;AAEhB,QAAK,KAAK,UAAU,MAAM,KAAM;AAE/B,UAAK,UAAU,IAAI,eAAgB,QAAQ,aAAc,GAAI;AAE5D,kBAAU,SAAS,KAAM,cAAc,IAAK,OAAQ,EAAE,aAAc,cAAe,CAAE;AAAA,MAEtF;AAAA,IAED,WAAY,KAAK,UAAU,MAAM,QAAS;AAEzC,YAAM,WAAY,UAAU,gBAAiB,EAAE,eAAgB,KAAK,WAAY;AAChF,gBAAU,kBAAmB,KAAK,MAAM,CAAE;AAC1C,gBAAU,kBAAmB,OAAO,UAAU,GAAG,CAAE,MAAM,CAAE;AAAA,IAE5D;AAEA,SAAK,cAAe,EAAE,MAAM,QAAQ,QAAQ,UAAU,CAAE;AAExD,qBAAiB,KAAM,QAAS;AAAA,EAEjC,OAAO;AAIN,QAAK,MAAM,gBAAgB,WAAW,MAAM,gBAAgB,OAAQ;AAEnE,qBAAe,SAAS;AAExB,gBAAU,cAAe,UAAU,MAAO;AAC1C,gBAAU,iBAAkB,KAAK,SAAS,KAAK,WAAW,cAAe;AAEzE,UAAK,eAAe,SAAS,GAAI;AAEhC,cAAM,SAAS,eAAgB,CAAE,EAAE;AAEnC,eAAO,8BAA+B,OAAO,kBAAmB,OAAO,MAAO,GAAG,eAAe,sBAAuB,OAAO,WAAY,CAAE;AAE5I,YAAK,aAAa,UAAU,aAAa,MAAO;AAE/C,eAAK,cAAe,EAAE,MAAM,YAAY,QAAQ,SAAS,CAAE;AAE3D,qBAAW,MAAM,SAAS;AAC1B,qBAAW;AAAA,QAEZ;AAEA,YAAK,aAAa,QAAS;AAE1B,eAAK,cAAe,EAAE,MAAM,WAAW,OAAe,CAAE;AAExD,qBAAW,MAAM,SAAS;AAC1B,qBAAW;AAAA,QAEZ;AAAA,MAED,OAAO;AAEN,YAAK,aAAa,MAAO;AAExB,eAAK,cAAe,EAAE,MAAM,YAAY,QAAQ,SAAS,CAAE;AAE3D,qBAAW,MAAM,SAAS;AAC1B,qBAAW;AAAA,QAEZ;AAAA,MAED;AAAA,IAED;AAAA,EAED;AAEA,mBAAiB,KAAM,QAAS;AAEjC;AAEA,SAAS,cAAe,OAAQ;AAE/B,QAAM,SAAS,KAAK;AACpB,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AAEvB,MAAK,KAAK,YAAY,MAAQ;AAE9B,OAAK,eAAgB,KAAM;AAC3B,OAAK,aAAc,KAAM;AAEzB,iBAAe,SAAS;AAExB,YAAU,cAAe,UAAU,MAAO;AAC1C,YAAU,iBAAkB,KAAK,SAAS,KAAK,WAAW,cAAe;AAEzE,MAAK,eAAe,SAAS,GAAI;AAEhC,QAAK,KAAK,mBAAmB,MAAO;AAInC,kBAAY,UAAW,eAAgB,CAAE,EAAE,MAAO;AAAA,IAEnD,OAAO;AAEN,kBAAY,eAAgB,CAAE,EAAE;AAAA,IAEjC;AAEA,WAAO,8BAA+B,OAAO,kBAAmB,OAAO,MAAO,GAAG,eAAe,sBAAuB,UAAU,WAAY,CAAE;AAE/I,QAAK,UAAU,IAAI,eAAgB,QAAQ,aAAc,GAAI;AAE5D,UAAK,KAAK,UAAU,MAAM,KAAM;AAE/B,uBAAe,KAAM,UAAU,OAAO,WAAY,EAAE,OAAO;AAC3D,gBAAQ,KAAM,aAAc,EAAE,IAAK,eAAe,sBAAuB,UAAU,WAAY,CAAE;AAAA,MAElG,WAAY,KAAK,UAAU,MAAM,QAAS;AAGzC,YAAI,IAAK,GAAG,GAAG,CAAE,EAAE,gBAAiB,OAAO,UAAW,EAAE,UAAU;AAClE,eAAO,IAAK,GAAG,GAAG,CAAE,EAAE,gBAAiB,OAAO,UAAW,EAAE,UAAU;AAAA,MAEtE;AAAA,IAED;AAEA,eAAW,MAAM,SAAS;AAE1B,SAAK,cAAe,EAAE,MAAM,aAAa,QAAQ,UAAU,CAAE;AAAA,EAE9D;AAEA,mBAAiB,KAAM,QAAS;AAEjC;AAEA,SAAS,kBAAkB;AAE1B,MAAK,KAAK,YAAY,MAAQ;AAE9B,MAAK,WAAY;AAEhB,SAAK,cAAe,EAAE,MAAM,WAAW,QAAQ,UAAU,CAAE;AAE3D,gBAAY;AAAA,EAEb;AAEA,OAAK,WAAW,MAAM,SAAS,WAAW,YAAY;AAEtD,OAAK,QAAQ,MAAM;AAEpB;AAEA,SAAS,cAAe,OAAQ;AAE/B,MAAK,KAAK,YAAY,MAAQ;AAE9B,QAAM,eAAe;AAEtB;AAEA,SAAS,UAAW,KAAK,QAAQ,MAAO;AAEvC,MAAK,IAAI,QAAU,SAAQ;AAE3B,MAAK,IAAI,WAAW,KAAO,QAAO;AAElC,SAAO,UAAW,IAAI,QAAQ,KAAM;AAErC;", "names": []}
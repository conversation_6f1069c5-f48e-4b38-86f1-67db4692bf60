import {
  Controls,
  MOUSE,
  Matrix4,
  Plane,
  Raycaster,
  TOUCH,
  Vector2,
  Vector3
} from "./chunk-C63TMWTL.js";

// node_modules/.pnpm/three@0.179.1/node_modules/three/examples/jsm/controls/DragControls.js
var _plane = new Plane();
var _pointer = new Vector2();
var _offset = new Vector3();
var _diff = new Vector2();
var _previousPointer = new Vector2();
var _intersection = new Vector3();
var _worldPosition = new Vector3();
var _inverseMatrix = new Matrix4();
var _up = new Vector3();
var _right = new Vector3();
var _selected = null;
var _hovered = null;
var _intersections = [];
var STATE = {
  NONE: -1,
  PAN: 0,
  ROTATE: 1
};
var DragControls = class extends Controls {
  /**
   * Constructs a new controls instance.
   *
   * @param {Array<Object3D>} objects - An array of draggable 3D objects.
   * @param {Camera} camera - The camera of the rendered scene.
   * @param {?HTMLDOMElement} [domElement=null] - The HTML DOM element used for event listeners.
   */
  constructor(objects, camera, domElement = null) {
    super(camera, domElement);
    this.objects = objects;
    this.recursive = true;
    this.transformGroup = false;
    this.rotateSpeed = 1;
    this.raycaster = new Raycaster();
    this.mouseButtons = { LEFT: MOUSE.PAN, MIDDLE: MOUSE.PAN, RIGHT: MOUSE.ROTATE };
    this.touches = { ONE: TOUCH.PAN };
    this._onPointerMove = onPointerMove.bind(this);
    this._onPointerDown = onPointerDown.bind(this);
    this._onPointerCancel = onPointerCancel.bind(this);
    this._onContextMenu = onContextMenu.bind(this);
    if (domElement !== null) {
      this.connect(domElement);
    }
  }
  connect(element) {
    super.connect(element);
    this.domElement.addEventListener("pointermove", this._onPointerMove);
    this.domElement.addEventListener("pointerdown", this._onPointerDown);
    this.domElement.addEventListener("pointerup", this._onPointerCancel);
    this.domElement.addEventListener("pointerleave", this._onPointerCancel);
    this.domElement.addEventListener("contextmenu", this._onContextMenu);
    this.domElement.style.touchAction = "none";
  }
  disconnect() {
    this.domElement.removeEventListener("pointermove", this._onPointerMove);
    this.domElement.removeEventListener("pointerdown", this._onPointerDown);
    this.domElement.removeEventListener("pointerup", this._onPointerCancel);
    this.domElement.removeEventListener("pointerleave", this._onPointerCancel);
    this.domElement.removeEventListener("contextmenu", this._onContextMenu);
    this.domElement.style.touchAction = "auto";
    this.domElement.style.cursor = "";
  }
  dispose() {
    this.disconnect();
  }
  _updatePointer(event) {
    const rect = this.domElement.getBoundingClientRect();
    _pointer.x = (event.clientX - rect.left) / rect.width * 2 - 1;
    _pointer.y = -(event.clientY - rect.top) / rect.height * 2 + 1;
  }
  _updateState(event) {
    let action;
    if (event.pointerType === "touch") {
      action = this.touches.ONE;
    } else {
      switch (event.button) {
        case 0:
          action = this.mouseButtons.LEFT;
          break;
        case 1:
          action = this.mouseButtons.MIDDLE;
          break;
        case 2:
          action = this.mouseButtons.RIGHT;
          break;
        default:
          action = null;
      }
    }
    switch (action) {
      case MOUSE.PAN:
      case TOUCH.PAN:
        this.state = STATE.PAN;
        break;
      case MOUSE.ROTATE:
      case TOUCH.ROTATE:
        this.state = STATE.ROTATE;
        break;
      default:
        this.state = STATE.NONE;
    }
  }
  getRaycaster() {
    console.warn("THREE.DragControls: getRaycaster() has been deprecated. Use controls.raycaster instead.");
    return this.raycaster;
  }
  setObjects(objects) {
    console.warn("THREE.DragControls: setObjects() has been deprecated. Use controls.objects instead.");
    this.objects = objects;
  }
  getObjects() {
    console.warn("THREE.DragControls: getObjects() has been deprecated. Use controls.objects instead.");
    return this.objects;
  }
  activate() {
    console.warn("THREE.DragControls: activate() has been renamed to connect().");
    this.connect();
  }
  deactivate() {
    console.warn("THREE.DragControls: deactivate() has been renamed to disconnect().");
    this.disconnect();
  }
  set mode(value) {
    console.warn("THREE.DragControls: The .mode property has been removed. Define the type of transformation via the .mouseButtons or .touches properties.");
  }
  get mode() {
    console.warn("THREE.DragControls: The .mode property has been removed. Define the type of transformation via the .mouseButtons or .touches properties.");
  }
};
function onPointerMove(event) {
  const camera = this.object;
  const domElement = this.domElement;
  const raycaster = this.raycaster;
  if (this.enabled === false) return;
  this._updatePointer(event);
  raycaster.setFromCamera(_pointer, camera);
  if (_selected) {
    if (this.state === STATE.PAN) {
      if (raycaster.ray.intersectPlane(_plane, _intersection)) {
        _selected.position.copy(_intersection.sub(_offset).applyMatrix4(_inverseMatrix));
      }
    } else if (this.state === STATE.ROTATE) {
      _diff.subVectors(_pointer, _previousPointer).multiplyScalar(this.rotateSpeed);
      _selected.rotateOnWorldAxis(_up, _diff.x);
      _selected.rotateOnWorldAxis(_right.normalize(), -_diff.y);
    }
    this.dispatchEvent({ type: "drag", object: _selected });
    _previousPointer.copy(_pointer);
  } else {
    if (event.pointerType === "mouse" || event.pointerType === "pen") {
      _intersections.length = 0;
      raycaster.setFromCamera(_pointer, camera);
      raycaster.intersectObjects(this.objects, this.recursive, _intersections);
      if (_intersections.length > 0) {
        const object = _intersections[0].object;
        _plane.setFromNormalAndCoplanarPoint(camera.getWorldDirection(_plane.normal), _worldPosition.setFromMatrixPosition(object.matrixWorld));
        if (_hovered !== object && _hovered !== null) {
          this.dispatchEvent({ type: "hoveroff", object: _hovered });
          domElement.style.cursor = "auto";
          _hovered = null;
        }
        if (_hovered !== object) {
          this.dispatchEvent({ type: "hoveron", object });
          domElement.style.cursor = "pointer";
          _hovered = object;
        }
      } else {
        if (_hovered !== null) {
          this.dispatchEvent({ type: "hoveroff", object: _hovered });
          domElement.style.cursor = "auto";
          _hovered = null;
        }
      }
    }
  }
  _previousPointer.copy(_pointer);
}
function onPointerDown(event) {
  const camera = this.object;
  const domElement = this.domElement;
  const raycaster = this.raycaster;
  if (this.enabled === false) return;
  this._updatePointer(event);
  this._updateState(event);
  _intersections.length = 0;
  raycaster.setFromCamera(_pointer, camera);
  raycaster.intersectObjects(this.objects, this.recursive, _intersections);
  if (_intersections.length > 0) {
    if (this.transformGroup === true) {
      _selected = findGroup(_intersections[0].object);
    } else {
      _selected = _intersections[0].object;
    }
    _plane.setFromNormalAndCoplanarPoint(camera.getWorldDirection(_plane.normal), _worldPosition.setFromMatrixPosition(_selected.matrixWorld));
    if (raycaster.ray.intersectPlane(_plane, _intersection)) {
      if (this.state === STATE.PAN) {
        _inverseMatrix.copy(_selected.parent.matrixWorld).invert();
        _offset.copy(_intersection).sub(_worldPosition.setFromMatrixPosition(_selected.matrixWorld));
      } else if (this.state === STATE.ROTATE) {
        _up.set(0, 1, 0).applyQuaternion(camera.quaternion).normalize();
        _right.set(1, 0, 0).applyQuaternion(camera.quaternion).normalize();
      }
    }
    domElement.style.cursor = "move";
    this.dispatchEvent({ type: "dragstart", object: _selected });
  }
  _previousPointer.copy(_pointer);
}
function onPointerCancel() {
  if (this.enabled === false) return;
  if (_selected) {
    this.dispatchEvent({ type: "dragend", object: _selected });
    _selected = null;
  }
  this.domElement.style.cursor = _hovered ? "pointer" : "auto";
  this.state = STATE.NONE;
}
function onContextMenu(event) {
  if (this.enabled === false) return;
  event.preventDefault();
}
function findGroup(obj, group = null) {
  if (obj.isGroup) group = obj;
  if (obj.parent === null) return group;
  return findGroup(obj.parent, group);
}
export {
  DragControls
};
//# sourceMappingURL=three_examples_jsm_controls_DragControls__js.js.map

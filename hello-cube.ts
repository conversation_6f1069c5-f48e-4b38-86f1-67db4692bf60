import * as THREE from "three";
const scene = new THREE.Scene();
// 添加光源, 默认朝向 (0,0,0)
{
  const light = new THREE.DirectionalLight(0xffffff, 3);
  light.position.set(1, 1, 1);
  scene.add(light);
}
// 添加相机, 相机朝向负Z方向, 也就是朝向你
const camera = new THREE.PerspectiveCamera(
  75,
  window.innerWidth / window.innerHeight,
  0.1,
  1000
);
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

const geometry = new THREE.BoxGeometry();
const material = new THREE.MeshPhongMaterial({
  color: 0x00ffff,
});
const cube = new THREE.Mesh(geometry, material);
cube.position.z = 1;
scene.add(cube);

camera.position.z = 4;
function animate() {
  requestAnimationFrame(animate);

  renderer.render(scene, camera);
}
animate();
